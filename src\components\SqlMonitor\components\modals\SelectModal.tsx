import type { AlertSend, DBConnection, OtherInfo, TaskAlert } from '@/types/task';
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { App, Button, Input, Modal, Space, Table, type TableColumnsType } from 'antd';
import type { TableRowSelection, TablePaginationConfig } from 'antd/es/table/interface';
import React from 'react';
import { createAlertSendTableColumns, createAlertTableColumns, createDBConnectionTableColumns, createOtherInfoTableColumns } from '@/components/SqlMonitor/components/columns';
import styles from '@/components/TaskFormModalsExtended.module.css';

// 联合类型用于 SelectModal
type SelectableData = TaskAlert | AlertSend | DBConnection | OtherInfo;

interface SelectModalProps {
  visible: boolean;
  type: 'alert' | 'alertSend' | 'dbConnection' | 'otherInfo';
  data: SelectableData[];
  selectedData?: SelectableData[]; // 已选择的数据，用于过滤
  onCancel: () => void;
  onSubmit: (selectedItems: SelectableData[]) => void;
  onSearch?: (searchText: string) => void; // 搜索回调函数
  multiple?: boolean;
}

// 选择已有数据Modal
export const SelectModal: React.FC<SelectModalProps> = ({ visible, type, data, selectedData = [], onCancel, onSubmit, onSearch, multiple = true }) => {
  const { message } = App.useApp();
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = React.useState<SelectableData[]>([]);
  const [pagination, setPagination] = React.useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  const [searchText, setSearchText] = React.useState<string>('');
  // 数据库连接的多字段搜索
  const [dbSearchFields, setDbSearchFields] = React.useState({
    name: '',
    dbType: '',
    host: '',
  });
  // 告警发送的双字段搜索
  const [alertSendSearchFields, setAlertSendSearchFields] = React.useState({
    name: '',
    type: '',
  });

  // 过滤掉已选择的数据
  const filteredData = React.useMemo(() => {
    let result = data;

    // 过滤掉已选择的数据
    if (selectedData && selectedData.length > 0) {
      const selectedIds = selectedData.map(item => item.id);
      result = result.filter(item => !selectedIds.includes(item.id));
    }

    return result;
  }, [data, selectedData]);

  // 更新分页总数
  React.useEffect(() => {
    setPagination(prev => ({
      ...prev,
      total: filteredData.length,
      current: 1, // 重置到第一页
    }));
  }, [filteredData]);

  const getColumns = (): TableColumnsType<SelectableData> => {
    // 为选择模态框提供简化的列配置，不需要编辑和删除操作
    const emptyProps = {
      filteredInfo: {},
      getSortOrder: () => null as 'ascend' | 'descend' | null,
      onEdit: () => {},
      onDelete: () => {},
    };

    switch (type) {
      case 'alert':
        return createAlertTableColumns(emptyProps).filter(col => col.key !== 'action') as TableColumnsType<SelectableData>;
      case 'alertSend':
        return createAlertSendTableColumns(emptyProps).filter(col => col.key !== 'action') as TableColumnsType<SelectableData>;
      case 'dbConnection':
        return createDBConnectionTableColumns(emptyProps).filter(col => col.key !== 'action') as TableColumnsType<SelectableData>;
      case 'otherInfo':
        return createOtherInfoTableColumns(emptyProps).filter(col => col.key !== 'action') as TableColumnsType<SelectableData>;
      default:
        return [];
    }
  };

  const getTitle = () => {
    switch (type) {
      case 'alert':
        return '选择告警';
      case 'alertSend':
        return '选择联系方式';
      case 'dbConnection':
        return '选择连接';
      case 'otherInfo':
        return '选择附加信息';
      default:
        return '选择数据';
    }
  };

  const handleSubmit = () => {
    if (selectedRows.length === 0) {
      const typeText = type === 'alert' ? '告警' : type === 'alertSend' ? '联系方式' : type === 'dbConnection' ? '连接' : '附加信息';
      message.warning(`请选择${typeText}`);
      return;
    }
    onSubmit(selectedRows);
    setSelectedRowKeys([]);
    setSelectedRows([]);
    // 重置分页状态
    setPagination({
      current: 1,
      pageSize: 10,
      total: 0,
    });
  };

  const handleCancel = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
    setSearchText('');
    setDbSearchFields({
      name: '',
      dbType: '',
      host: '',
    });
    setAlertSendSearchFields({
      name: '',
      type: '',
    });
    // 重置分页状态
    setPagination({
      current: 1,
      pageSize: 10,
      total: 0,
    });
    onCancel();
  };

  // 重置选择
  const handleResetSelection = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  // 处理搜索
  const handleSearch = () => {
    if (type === 'dbConnection') {
      // 数据库连接的多字段搜索
      const { name, dbType, host } = dbSearchFields;
      if (!name.trim() && !dbType.trim() && !host.trim()) {
        message.warning('请输入搜索内容');
        return;
      }
      if (onSearch) {
        onSearch(
          JSON.stringify({
            name: name.trim(),
            dbType: dbType.trim(),
            host: host.trim(),
          })
        );
      }
    } else if (type === 'alertSend') {
      // 告警发送的双字段搜索
      const { name, type: sendType } = alertSendSearchFields;
      if (!name.trim() && !sendType.trim()) {
        message.warning('请输入搜索内容');
        return;
      }
      if (onSearch) {
        onSearch(
          JSON.stringify({
            name: name.trim(),
            type: sendType.trim(),
          })
        );
      }
    } else {
      // 其他类型的单字段搜索（告警、其他信息等）
      if (!searchText.trim()) {
        message.warning('请输入搜索内容');
        return;
      }
      if (onSearch) {
        onSearch(searchText.trim());
      }
    }
  };

  // 获取搜索框占位符文本
  const getSearchPlaceholder = () => {
    switch (type) {
      case 'alert':
        return '请输入告警名称';
      case 'dbConnection':
        return '请输入连接名称/数据库类型/主机地址';
      case 'alertSend':
        return '请输入发送名称';
      case 'otherInfo':
        return '请输入信息名称';
      default:
        return '请输入搜索内容';
    }
  };

  // 重置搜索
  const handleResetSearch = () => {
    if (type === 'dbConnection') {
      const { name, dbType, host } = dbSearchFields;
      if (!name.trim() && !dbType.trim() && !host.trim()) {
        message.info('搜索条件已为空');
        return;
      }
      setDbSearchFields({
        name: '',
        dbType: '',
        host: '',
      });
      if (onSearch) {
        onSearch(''); // 传递空字符串表示重置搜索
      }
    } else if (type === 'alertSend') {
      const { name, type: sendType } = alertSendSearchFields;
      if (!name.trim() && !sendType.trim()) {
        message.info('搜索条件已为空');
        return;
      }
      setAlertSendSearchFields({
        name: '',
        type: '',
      });
      if (onSearch) {
        onSearch(''); // 传递空字符串表示重置搜索
      }
    } else {
      // 其他类型的单字段重置（告警、其他信息等）
      if (!searchText.trim()) {
        message.info('搜索条件已为空');
        return;
      }
      setSearchText('');
      if (onSearch) {
        onSearch(''); // 传递空字符串表示重置搜索
      }
    }
  };

  // 处理分页变化
  const handleTableChange = (paginationConfig: TablePaginationConfig) => {
    const newPagination = {
      current: paginationConfig.current || 1,
      pageSize: paginationConfig.pageSize || 10,
      total: pagination.total,
    };
    setPagination(newPagination);

    // 如果页码或页大小发生变化，重新加载数据
    if (newPagination.current !== pagination.current || newPagination.pageSize !== pagination.pageSize) {
      // 构建当前搜索参数
      let searchParams = {};
      if (type === 'dbConnection') {
        const { name, dbType, host } = dbSearchFields;
        if (name.trim() || dbType.trim() || host.trim()) {
          searchParams = {
            name: name.trim() || undefined,
            db_type: dbType.trim() || undefined,
            host: host.trim() || undefined,
          };
        }
      } else if (type === 'alertSend') {
        const { name, type: sendType } = alertSendSearchFields;
        if (name.trim() || sendType.trim()) {
          searchParams = {
            name: name.trim() || undefined,
            receive_type: sendType.trim() || undefined,
          };
        }
      } else if ((type === 'alert' || type === 'otherInfo') && searchText.trim()) {
        searchParams = {
          name: searchText.trim(),
        };
      }

      loadData(searchParams);
    }
  };

  const rowSelection: TableRowSelection<SelectableData> = {
    type: multiple ? 'checkbox' : 'radio',
    selectedRowKeys,
    onChange: (keys: React.Key[], rows: SelectableData[]) => {
      setSelectedRowKeys(keys);
      setSelectedRows(rows);
    },
  };

  return (
    <Modal
      title={getTitle()}
      open={visible}
      onCancel={handleCancel}
      maskClosable={false} // 点击遮蔽区域不退出
      footer={
        <div className="flex justify-end">
          <Space size="middle">
            <Button onClick={handleCancel}>取消</Button>
            <Button onClick={handleResetSelection}>重置选择</Button>
            <Button type="primary" onClick={handleSubmit}>
              确认选择 ({selectedRows.length})
            </Button>
          </Space>
        </div>
      }
      width={1200} // 增加Modal宽度以适应更多列
      styles={{
        body: {
          padding: '16px 24px',
        },
      }}
    >
      {/* 搜索框和搜索按钮 */}
      <div className="mb-4">
        {(type === 'alert' || type === 'otherInfo') && (
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <Space>
                <Input
                  placeholder={getSearchPlaceholder()}
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={e => setSearchText(e.target.value)}
                  style={{
                    width: type === 'otherInfo' ? 250 : 280,
                  }}
                  className={styles.searchInput || 'search-input'}
                  allowClear
                  onPressEnter={handleSearch}
                  autoComplete="off"
                  autoCorrect="off"
                  autoCapitalize="off"
                  spellCheck={false}
                />
              </Space>
            </div>
            <div>
              <Space>
                <Button type="primary" onClick={handleSearch} className={styles.searchButton || 'search-button'}>
                  搜索
                </Button>
                <Button icon={<ReloadOutlined />} onClick={handleResetSearch} className={styles.resetButton || 'reset-button'}>
                  重置搜索
                </Button>
              </Space>
            </div>
          </div>
        )}

        {type === 'dbConnection' && (
          <div>
            <div className="flex justify-between items-center mb-3">
              <div className="flex items-center gap-3">
                <Input
                  placeholder="连接名称"
                  prefix={<SearchOutlined />}
                  value={dbSearchFields.name}
                  onChange={e =>
                    setDbSearchFields(prev => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                  style={{ width: 180 }}
                  className={styles.searchInput || 'search-input'}
                  allowClear
                  onPressEnter={handleSearch}
                  autoComplete="off"
                  autoCorrect="off"
                  autoCapitalize="off"
                  spellCheck={false}
                />
                <Input
                  placeholder="数据库类型"
                  value={dbSearchFields.dbType}
                  onChange={e =>
                    setDbSearchFields(prev => ({
                      ...prev,
                      dbType: e.target.value,
                    }))
                  }
                  style={{ width: 120 }}
                  className={styles.searchInput || 'search-input'}
                  allowClear
                  onPressEnter={handleSearch}
                  autoComplete="off"
                  autoCorrect="off"
                  autoCapitalize="off"
                  spellCheck={false}
                />
                <Input
                  placeholder="主机地址"
                  value={dbSearchFields.host}
                  onChange={e =>
                    setDbSearchFields(prev => ({
                      ...prev,
                      host: e.target.value,
                    }))
                  }
                  style={{ width: 150 }}
                  className={styles.searchInput || 'search-input'}
                  allowClear
                  onPressEnter={handleSearch}
                  autoComplete="off"
                  autoCorrect="off"
                  autoCapitalize="off"
                  spellCheck={false}
                />
              </div>
              <div>
                <Space>
                  <Button type="primary" onClick={handleSearch} className={styles.searchButton || 'search-button'}>
                    搜索
                  </Button>
                  <Button icon={<ReloadOutlined />} onClick={handleResetSearch} className={styles.resetButton || 'reset-button'}>
                    重置搜索
                  </Button>
                </Space>
              </div>
            </div>
          </div>
        )}

        {type === 'alertSend' && (
          <div>
            <div className="flex justify-between items-center mb-3">
              <div className="flex items-center gap-3">
                <Input
                  placeholder="发送名称"
                  prefix={<SearchOutlined />}
                  value={alertSendSearchFields.name}
                  onChange={e =>
                    setAlertSendSearchFields(prev => ({
                      ...prev,
                      name: e.target.value,
                    }))
                  }
                  style={{ width: 200 }}
                  className={styles.searchInput || 'search-input'}
                  allowClear
                  onPressEnter={handleSearch}
                  autoComplete="off"
                  autoCorrect="off"
                  autoCapitalize="off"
                  spellCheck={false}
                />
                <Input
                  placeholder="发送类型"
                  value={alertSendSearchFields.type}
                  onChange={e =>
                    setAlertSendSearchFields(prev => ({
                      ...prev,
                      type: e.target.value,
                    }))
                  }
                  style={{ width: 150 }}
                  className={styles.searchInput || 'search-input'}
                  allowClear
                  onPressEnter={handleSearch}
                  autoComplete="off"
                  autoCorrect="off"
                  autoCapitalize="off"
                  spellCheck={false}
                />
              </div>
              <div>
                <Space>
                  <Button type="primary" onClick={handleSearch} className={styles.searchButton || 'search-button'}>
                    搜索
                  </Button>
                  <Button icon={<ReloadOutlined />} onClick={handleResetSearch} className={styles.resetButton || 'reset-button'}>
                    重置搜索
                  </Button>
                </Space>
              </div>
            </div>
          </div>
        )}
      </div>

      {selectedData && selectedData.length > 0 && (
        <div className={styles.infoBanner || 'info-banner'}>
          <div className={styles.infoText || 'info-text'}>
            <span
              style={{
                fontWeight: 600,
              }}
            >
              提示：
            </span>
            已过滤掉 {selectedData.length} 个已选择的项目， 当前可选择 {filteredData.length} 个项目
          </div>
        </div>
      )}
      <div className={styles.tableContainer || 'table-container'}>
        <Table
          dataSource={filteredData}
          columns={getColumns()}
          rowKey="id"
          rowSelection={rowSelection}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100'],
            size: 'small',
          }}
          onChange={handleTableChange}
          scroll={{
            y: 450,
            x: 'max-content',
          }}
          size="small"
          bordered
          rowClassName={(_, index) => (index % 2 === 0 ? styles.tableRowLight || 'table-row-light' : styles.tableRowDark || 'table-row-dark')}
          style={{
            backgroundColor: '#fff',
          }}
          tableLayout="fixed"
        />
      </div>
    </Modal>
  );
};
